#!/bin/bash

# IDE User Data Cleanup Script
# This script removes all user data and cache files for Windsurf or Cursor

set -e  # Exit on any error

# Function to show usage
show_usage() {
    echo "=============================================="
    echo "        IDE User Data Cleanup Script"
    echo "=============================================="
    echo ""
    echo "Usage: $0 [windsurf|cursor|both]"
    echo ""
    echo "Options:"
    echo "  windsurf  - Clean only Windsurf data"
    echo "  cursor    - Clean only Cursor data"
    echo "  both      - Clean both Windsurf and Cursor data"
    echo ""
    echo "If no option is provided, you will be prompted to choose."
    echo ""
}

# Function to safely remove directory if it exists
remove_dir_if_exists() {
    local dir="$1"
    local description="$2"
    
    if [ -d "$dir" ]; then
        echo "Removing $description: $dir"
        rm -rf "$dir"
        echo "Successfully removed $description"
    else
        echo "$description not found: $dir"
    fi
    echo ""
}

# Function to ask for confirmation
confirm_action() {
    local target="$1"
    echo "WARNING: This will permanently delete all $target user data!"
    
    case "$target" in
        "Windsurf")
            echo "   - Project-specific settings (.windsurf/ and .codeium/ in current directory)"
            echo "   - Global Windsurf application data"
            echo "   - Windsurf cache files"
            ;;
        "Cursor")
            echo "   - Project-specific settings (.cursor* files in home directory)"
            echo "   - Global Cursor application data"
            echo "   - Cursor cache files"
            ;;
        "Windsurf and Cursor")
            echo "   - All Windsurf data (project settings, app data, cache)"
            echo "   - All Cursor data (project settings, app data, cache)"
            ;;
    esac
    
    echo ""
    printf "Are you sure you want to continue? (y/N): "
    read -r reply
    echo ""
    
    if [[ ! $reply =~ ^[Yy]$ ]]; then
        echo "Operation cancelled by user"
        exit 0
    fi
    echo ""
}

# Windsurf cleanup function
cleanup_windsurf() {
    echo "Starting Windsurf cleanup..."
    echo ""
    
    # Remove project-specific directories from current directory
    echo "Cleaning project-specific data in current directory..."
    remove_dir_if_exists ".windsurf" "Project Windsurf settings"
    remove_dir_if_exists ".codeium" "Project Codeium settings"
    
    # Remove global Windsurf application data
    echo "Cleaning global Windsurf application data..."
    windsurf_app_support="$HOME/Library/Application Support/Windsurf"
    remove_dir_if_exists "$windsurf_app_support" "Windsurf application data"
    
    # Remove Windsurf cache
    echo "Cleaning Windsurf cache..."
    windsurf_cache="$HOME/Library/Caches/Windsurf"
    remove_dir_if_exists "$windsurf_cache" "Windsurf cache"
    
    # Also check for other potential cache locations
    windsurf_cache_alt="$HOME/Library/Cache/Windsurf"
    remove_dir_if_exists "$windsurf_cache_alt" "Windsurf cache (alternative location)"
    
    echo "Windsurf cleanup completed successfully!"
}

# Cursor cleanup function
cleanup_cursor() {
    echo "Starting Cursor cleanup..."
    echo ""
    
    # Remove .cursor* files from home directory
    echo "Cleaning Cursor project-specific data in home directory..."
    for cursor_file in "$HOME"/.cursor*; do
        if [ -e "$cursor_file" ]; then
            echo "Removing Cursor file: $cursor_file"
            rm -rf "$cursor_file"
            echo "Successfully removed $(basename "$cursor_file")"
        fi
    done
    echo ""
    
    # Remove global Cursor application data
    echo "Cleaning global Cursor application data..."
    cursor_app_support="$HOME/Library/Application Support/Cursor"
    remove_dir_if_exists "$cursor_app_support" "Cursor application data"
    
    # Remove Cursor cache
    echo "Cleaning Cursor cache..."
    cursor_cache="$HOME/Library/Caches/Cursor"
    remove_dir_if_exists "$cursor_cache" "Cursor cache"
    
    # Also check for other potential cache locations
    cursor_cache_alt="$HOME/Library/Cache/Cursor"
    remove_dir_if_exists "$cursor_cache_alt" "Cursor cache (alternative location)"
    
    echo "Cursor cleanup completed successfully!"
}

# Function to choose cleanup target interactively
choose_target() {
    clear
    echo "=============================================="
    echo "        IDE User Data Cleanup Script"
    echo "=============================================="
    echo ""
    echo "Which IDE data would you like to clean?"
    echo ""
    echo "  [1] Windsurf only"
    echo "      - Remove .windsurf/ and .codeium/ folders"
    echo "      - Remove Windsurf app data and cache"
    echo ""
    echo "  [2] Cursor only"
    echo "      - Remove .cursor* files from home directory"
    echo "      - Remove Cursor app data and cache"
    echo ""
    echo "  [3] Both Windsurf and Cursor"
    echo "      - Clean all data from both IDEs"
    echo ""
    echo "  [4] Cancel"
    echo "      - Exit without making any changes"
    echo ""
    echo "=============================================="
    echo ""
    printf "Please choose an option (1-4): "
    read choice
    echo ""
    
    case $choice in
        1) echo "windsurf" ;;
        2) echo "cursor" ;;
        3) echo "both" ;;
        4) echo "cancel" ;;
        *)
            echo "ERROR: Invalid option '$choice'. Please choose 1, 2, 3, or 4."
            echo ""
            printf "Press Enter to try again..."
            read -r
            choose_target
            return
            ;;
    esac
}

# Check if running on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    echo "ERROR: This script is designed for macOS only."
    echo "The paths used are specific to macOS file system structure."
    exit 1
fi

# Parse command line arguments or show interactive menu
TARGET=""
if [ $# -eq 0 ]; then
    # No arguments provided, show interactive menu
    TARGET=$(choose_target)
else
    # Use command line argument
    case "$1" in
        "windsurf"|"cursor"|"both")
            TARGET="$1"
            ;;
        "-h"|"--help"|"help")
            show_usage
            exit 0
            ;;
        *)
            echo "ERROR: Invalid option '$1'"
            echo ""
            show_usage
            exit 1
            ;;
    esac
fi

# Handle user choice
case "$TARGET" in
    "windsurf")
        confirm_action "Windsurf"
        cleanup_windsurf
        echo ""
        echo "All done! Windsurf user data has been cleaned up."
        echo "Note: You may need to restart Windsurf and reconfigure your settings."
        ;;
    "cursor")
        confirm_action "Cursor"
        cleanup_cursor
        echo ""
        echo "All done! Cursor user data has been cleaned up."
        echo "Note: You may need to restart Cursor and reconfigure your settings."
        ;;
    "both")
        confirm_action "Windsurf and Cursor"
        cleanup_windsurf
        echo ""
        cleanup_cursor
        echo ""
        echo "All done! Both Windsurf and Cursor user data have been cleaned up."
        echo "Note: You may need to restart both IDEs and reconfigure your settings."
        ;;
    "cancel")
        echo "Operation cancelled by user"
        exit 0
        ;;
    "invalid")
        echo "Invalid option selected"
        exit 1
        ;;
    *)
        echo "Unexpected error occurred"
        exit 1
        ;;
esac
