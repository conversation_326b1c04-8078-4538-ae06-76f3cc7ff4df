#!/bin/bash

# Windsurf User Data Cleanup Script
# This script removes all Windsurf user data and cache files

set -e  # Exit on any error

echo "🧹 Windsurf User Data Cleanup Script"
echo "===================================="
echo ""

# Function to safely remove directory if it exists
remove_dir_if_exists() {
    local dir="$1"
    local description="$2"
    
    if [ -d "$dir" ]; then
        echo "🗑️  Removing $description: $dir"
        rm -rf "$dir"
        echo "✅ Successfully removed $description"
    else
        echo "ℹ️  $description not found: $dir"
    fi
    echo ""
}

# Function to ask for confirmation
confirm_action() {
    echo "⚠️  WARNING: This will permanently delete all Windsurf user data!"
    echo "   - Project-specific settings (.windsurf/ and .codeium/ in current directory)"
    echo "   - Global Windsurf application data"
    echo "   - Windsurf cache files"
    echo ""
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo ""
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ Operation cancelled by user"
        exit 0
    fi
    echo ""
}

# Main cleanup function
cleanup_windsurf() {
    echo "🚀 Starting Windsurf cleanup..."
    echo ""
    
    # Remove project-specific directories from current directory
    echo "📁 Cleaning project-specific data in current directory..."
    remove_dir_if_exists ".windsurf" "Project Windsurf settings"
    remove_dir_if_exists ".codeium" "Project Codeium settings"
    
    # Remove global Windsurf application data
    echo "🌐 Cleaning global Windsurf application data..."
    windsurf_app_support="$HOME/Library/Application Support/Windsurf"
    remove_dir_if_exists "$windsurf_app_support" "Windsurf application data"
    
    # Remove Windsurf cache
    echo "💾 Cleaning Windsurf cache..."
    windsurf_cache="$HOME/Library/Caches/Windsurf"
    remove_dir_if_exists "$windsurf_cache" "Windsurf cache"
    
    # Also check for other potential cache locations
    windsurf_cache_alt="$HOME/Library/Cache/Windsurf"
    remove_dir_if_exists "$windsurf_cache_alt" "Windsurf cache (alternative location)"
    
    echo "✨ Windsurf cleanup completed successfully!"
    echo ""
    echo "📝 Note: You may need to restart Windsurf and reconfigure your settings."
}

# Check if running on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    echo "❌ Error: This script is designed for macOS only."
    echo "   The paths used are specific to macOS file system structure."
    exit 1
fi

# Show confirmation dialog
confirm_action

# Perform cleanup
cleanup_windsurf

echo "🎉 All done! Windsurf user data has been cleaned up."
